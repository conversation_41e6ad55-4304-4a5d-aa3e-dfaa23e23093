package com.dyd.di.marketing.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.dyd.di.marketing.domain.DiTerritoryProductMapping;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 产品矩阵映射 Mapper
 */
public interface DiTerritoryProductMappingMapper extends BaseMapper<DiTerritoryProductMapping> {

    /**
     * 批量插入产品映射
     * @param mappingList 映射列表
     * @return 影响行数
     */
    int insertBatch(@Param("list") List<DiTerritoryProductMapping> mappingList);

}