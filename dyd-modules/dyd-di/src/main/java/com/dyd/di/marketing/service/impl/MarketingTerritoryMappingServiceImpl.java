package com.dyd.di.marketing.service.impl;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.dyd.common.core.domain.R;
import com.dyd.common.core.exception.ServiceException;
import com.dyd.common.core.utils.StringUtils;
import com.dyd.di.marketing.domain.DiTerritoryMapping;
import com.dyd.di.marketing.domain.DiTerritoryProductMapping;
import com.dyd.di.marketing.domain.vo.MarketingTerritoryMappingVO;
import com.dyd.di.marketing.mapper.DiTerritoryMappingMapper;
import com.dyd.di.marketing.mapper.DiTerritoryProductMappingMapper;
import com.dyd.di.marketing.service.MarketingTerritoryMappingService;
import com.dyd.di.matrix.dto.MatrixProductDetailDTO;
import com.dyd.di.matrix.pojo.response.QueryProductByTerritoryResponse;
import com.dyd.di.matrix.service.DiMatrixProductService;
import com.dyd.di.matrix.service.DiMatrixTerritoryService;
import com.dyd.di.matrix.vo.OperateMatrixProductVO;
import com.dyd.di.storage.redisson.RedissonLock;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;

@Slf4j
@Service
public class MarketingTerritoryMappingServiceImpl implements MarketingTerritoryMappingService {

    @Autowired
    private DiTerritoryMappingMapper territoryMappingMapper;
    @Autowired
    private DiTerritoryProductMappingMapper diTerritoryProductMappingMapper;
    @Autowired
    private DiMatrixProductService diMatrixProductService;
    @Autowired
    private DiMatrixTerritoryService diMatrixTerritoryService;
    @Autowired
    private RedissonLock redissonLock;

    /**
     * 添加营销版图映射关系
     * 使用Redis分布式锁确保高并发场景下的数据一致性
     * @param reqVO 请求参数
     */
    @Override
    public void add(MarketingTerritoryMappingVO reqVO) {
        // 使用 refId + territoryId 作为锁的key，确保同一个业务版图的操作串行化
        String lockKey = generateLockKey(reqVO.getRefId(), reqVO.getTerritoryId());

        try {
            redissonLock.lock(lockKey, () -> {
                addWithTransaction(reqVO);
                return null;
            }, 30); // 30秒超时
        } catch (Exception e) {
            log.error("添加营销版图映射失败，refId: {}, territoryId: {}, 错误: {}",
                reqVO.getRefId(), reqVO.getTerritoryId(), e.getMessage(), e);
            throw new ServiceException("添加营销版图映射失败: " + e.getMessage());
        }
    }

    /**
     * 在事务中执行添加操作
     * @param reqVO 请求参数
     */
    @Transactional(rollbackFor = Exception.class)
    public void addWithTransaction(MarketingTerritoryMappingVO reqVO) {
        // 1. 处理业务版图映射
        handleTerritoryMapping(reqVO);

        // 2. 处理产品映射关系
        if (reqVO.getProductId() != null) {
            // 单个产品处理
            handleSingleProductMapping(reqVO);
        } else {
            // 批量产品处理
            handleBatchProductMappingWithLock(reqVO);
        }
    }

    /**
     * 生成Redis分布式锁的key
     * @param refId 引用ID
     * @param territoryId 业务版图ID
     * @return 锁的key
     */
    private String generateLockKey(String refId, Long territoryId) {
        return "territory_mapping_lock:" + refId + ":" + territoryId;
    }

    /**
     * 处理业务版图映射
     * @param reqVO 请求参数
     */
    private void handleTerritoryMapping(MarketingTerritoryMappingVO reqVO) {
        Long count = territoryMappingMapper.selectCount(Wrappers.<DiTerritoryMapping>lambdaQuery()
                .eq(DiTerritoryMapping::getRefId, reqVO.getRefId())
                .eq(DiTerritoryMapping::getTerritoryId, reqVO.getTerritoryId())
        );

        if (count <= 0) {
            DiTerritoryMapping territoryMapping = DiTerritoryMapping.builder()
                    .refId(reqVO.getRefId())
                    .territoryId(reqVO.getTerritoryId())
                    .territoryJson(reqVO.getTerritoryJson())
                    .territoryInfo(JSON.toJSONString(diMatrixTerritoryService.getMatrixTerritoryInfo(reqVO.getTerritoryId())))
                    .build();
            territoryMappingMapper.insert(territoryMapping);
        }
    }

    /**
     * 处理单个产品映射
     * @param reqVO 请求参数
     */
    private void handleSingleProductMapping(MarketingTerritoryMappingVO reqVO) {
        // 删除已存在的相同映射
        diTerritoryProductMappingMapper.delete(Wrappers.<DiTerritoryProductMapping>lambdaQuery()
                .eq(DiTerritoryProductMapping::getRefId, reqVO.getRefId())
                .eq(DiTerritoryProductMapping::getTerritoryId, reqVO.getTerritoryId())
                .eq(DiTerritoryProductMapping::getProductId, reqVO.getProductId())
        );

        // 插入新的映射关系
        DiTerritoryProductMapping territoryProductMapping = getTerritoryProductMapping(reqVO);
        diTerritoryProductMappingMapper.insert(territoryProductMapping);
    }

    /**
     * 处理批量产品映射（带锁保护）
     * @param reqVO 请求参数
     */
    private void handleBatchProductMappingWithLock(MarketingTerritoryMappingVO reqVO) {
        QueryProductByTerritoryResponse res = JSON.parseObject(reqVO.getProductJson(), QueryProductByTerritoryResponse.class);
        if (res == null || CollectionUtils.isEmpty(res.getData())) {
            return;
        }

        // 删除已存在的相同refId和territoryId的所有映射
        diTerritoryProductMappingMapper.delete(Wrappers.<DiTerritoryProductMapping>lambdaQuery()
                .eq(DiTerritoryProductMapping::getRefId, reqVO.getRefId())
                .eq(DiTerritoryProductMapping::getTerritoryId, reqVO.getTerritoryId())
        );

        // 为每个产品创建独立的映射对象，避免共享状态导致的数据重复问题
        List<DiTerritoryProductMapping> mappingList = res.getData().stream()
                .map(productData -> createProductMapping(reqVO, res, productData))
                .toList();

        // 使用更安全的插入方式，逐个插入并处理冲突
        insertProductMappingsSafely(mappingList);
    }

    /**
     * 安全地插入产品映射，处理高并发场景下的冲突
     * @param mappingList 映射列表
     */
    private void insertProductMappingsSafely(List<DiTerritoryProductMapping> mappingList) {
        if (CollectionUtils.isEmpty(mappingList)) {
            return;
        }

        for (DiTerritoryProductMapping mapping : mappingList) {
            try {
                // 使用 INSERT IGNORE 或者先查询再插入的方式
                insertSingleMappingWithRetry(mapping);
            } catch (Exception e) {
                log.warn("插入产品映射失败，refId: {}, territoryId: {}, productId: {}, 尝试更新操作, 错误: {}",
                    mapping.getRefId(), mapping.getTerritoryId(), mapping.getProductId(), e.getMessage());

                // 如果插入失败，尝试更新操作
                try {
                    updateExistingMapping(mapping);
                } catch (Exception updateEx) {
                    log.error("更新产品映射也失败，refId: {}, territoryId: {}, productId: {}, 错误: {}",
                        mapping.getRefId(), mapping.getTerritoryId(), mapping.getProductId(), updateEx.getMessage());
                }
            }
        }
    }

    /**
     * 安全地插入单个映射，带重试机制
     * @param mapping 映射对象
     */
    private void insertSingleMappingWithRetry(DiTerritoryProductMapping mapping) {
        // 先检查是否已存在
        Long existingCount = diTerritoryProductMappingMapper.selectCount(
            Wrappers.<DiTerritoryProductMapping>lambdaQuery()
                .eq(DiTerritoryProductMapping::getRefId, mapping.getRefId())
                .eq(DiTerritoryProductMapping::getTerritoryId, mapping.getTerritoryId())
                .eq(DiTerritoryProductMapping::getProductId, mapping.getProductId())
        );

        if (existingCount == 0) {
            diTerritoryProductMappingMapper.insert(mapping);
        } else {
            log.debug("产品映射已存在，跳过插入：refId={}, territoryId={}, productId={}",
                mapping.getRefId(), mapping.getTerritoryId(), mapping.getProductId());
        }
    }

    /**
     * 更新已存在的映射
     * @param mapping 映射对象
     */
    private void updateExistingMapping(DiTerritoryProductMapping mapping) {
        diTerritoryProductMappingMapper.update(mapping,
            Wrappers.<DiTerritoryProductMapping>lambdaUpdate()
                .eq(DiTerritoryProductMapping::getRefId, mapping.getRefId())
                .eq(DiTerritoryProductMapping::getTerritoryId, mapping.getTerritoryId())
                .eq(DiTerritoryProductMapping::getProductId, mapping.getProductId())
        );
    }

    /**
     * 为单个产品创建映射对象
     * @param originalVO 原始VO对象
     * @param res 产品响应数据
     * @param productData 单个产品数据
     * @return 产品映射对象
     */
    private DiTerritoryProductMapping createProductMapping(MarketingTerritoryMappingVO originalVO,
                                                          QueryProductByTerritoryResponse res,
                                                          Map<String, String> productData) {
        // 创建单个产品的响应对象
        QueryProductByTerritoryResponse singleProductResponse = QueryProductByTerritoryResponse.builder()
                .titleMap(res.getTitleMap())
                .data(Lists.newArrayList(productData))
                .total(1L)
                .build();

        // 创建独立的VO对象，避免修改原始对象
        MarketingTerritoryMappingVO productVO = new MarketingTerritoryMappingVO();
        productVO.setRefId(originalVO.getRefId());
        productVO.setTerritoryId(originalVO.getTerritoryId());
        productVO.setTerritoryJson(originalVO.getTerritoryJson());
        productVO.setProductId(Long.parseLong(productData.get("id")));
        productVO.setProductJson(JSON.toJSONString(singleProductResponse));

        return getTerritoryProductMapping(productVO);
    }

    /**
     * 根据VO创建产品映射对象
     * @param reqVO 请求参数
     * @return 产品映射对象
     */
    private DiTerritoryProductMapping getTerritoryProductMapping(MarketingTerritoryMappingVO reqVO) {
        // 获取产品详情，注意处理返回类型和空值
        R<MatrixProductDetailDTO> productDetailResult = diMatrixProductService.getMatrixProductDetail(
                OperateMatrixProductVO.builder().id(reqVO.getProductId()).build());

        MatrixProductDetailDTO data = null;
        if (productDetailResult != null && productDetailResult.isSuccess() && productDetailResult.getData() != null) {
            data = productDetailResult.getData();
        }

        return DiTerritoryProductMapping.builder()
                .refId(reqVO.getRefId())
                .territoryId(reqVO.getTerritoryId())
                .productId(reqVO.getProductId())
                .productJson(reqVO.getProductJson())
                .productInfo(data != null ? JSON.toJSONString(data) : null)
                .build();
    }

    /**
     * 删除营销版图映射关系
     * 使用Redis分布式锁确保高并发场景下的数据一致性，避免外键约束违反
     * @param reqVO 请求参数
     */
    @Override
    public void del(MarketingTerritoryMappingVO reqVO) {
        // 参数校验
        if (reqVO == null || StringUtils.isEmpty(reqVO.getRefId()) || reqVO.getTerritoryId() == null) {
            throw new ServiceException("删除参数不能为空：refId和territoryId必填");
        }

        // 使用 refId + territoryId 作为锁的key，确保同一个业务版图的操作串行化
        String lockKey = generateLockKey(reqVO.getRefId(), reqVO.getTerritoryId());

        try {
            redissonLock.lock(lockKey, () -> {
                delWithTransaction(reqVO);
                return null;
            }, 30); // 30秒超时
        } catch (Exception e) {
            log.error("删除营销版图映射失败，refId: {}, territoryId: {}, productId: {}, 错误: {}",
                reqVO.getRefId(), reqVO.getTerritoryId(), reqVO.getProductId(), e.getMessage(), e);
            throw new ServiceException("删除营销版图映射失败: " + e.getMessage());
        }
    }

    /**
     * 在事务中执行删除操作
     * 严格按照外键约束要求的顺序执行删除：先删子表，再删父表
     * @param reqVO 请求参数
     */
    @Transactional(rollbackFor = Exception.class)
    public void delWithTransaction(MarketingTerritoryMappingVO reqVO) {
        try {
            if (Objects.nonNull(reqVO.getProductId())) {
                // 删除指定的单个产品映射
                log.info("删除单个产品映射，refId: {}, territoryId: {}, productId: {}",
                    reqVO.getRefId(), reqVO.getTerritoryId(), reqVO.getProductId());

                int deletedCount = diTerritoryProductMappingMapper.delete(Wrappers.<DiTerritoryProductMapping>lambdaQuery()
                        .eq(DiTerritoryProductMapping::getRefId, reqVO.getRefId())
                        .eq(DiTerritoryProductMapping::getTerritoryId, reqVO.getTerritoryId())
                        .eq(DiTerritoryProductMapping::getProductId, reqVO.getProductId())
                );

                log.info("删除单个产品映射完成，删除记录数: {}", deletedCount);

            } else if (StringUtils.hasText(reqVO.getProductIds())) {
                // 删除指定的多个产品映射
                List<Long> ids = Arrays.stream(reqVO.getProductIds().split(","))
                    .map(String::trim)
                    .filter(StringUtils::hasText)
                    .map(Long::parseLong)
                    .toList();

                if (!ids.isEmpty()) {
                    log.info("删除多个产品映射，refId: {}, territoryId: {}, productIds: {}",
                        reqVO.getRefId(), reqVO.getTerritoryId(), ids);

                    int deletedCount = diTerritoryProductMappingMapper.delete(Wrappers.<DiTerritoryProductMapping>lambdaQuery()
                            .eq(DiTerritoryProductMapping::getRefId, reqVO.getRefId())
                            .eq(DiTerritoryProductMapping::getTerritoryId, reqVO.getTerritoryId())
                            .in(DiTerritoryProductMapping::getProductId, ids)
                    );

                    log.info("删除多个产品映射完成，删除记录数: {}", deletedCount);
                }

            } else {
                // 删除整个业务版图映射（包括所有相关的产品映射）
                // 由于外键约束是基于ref_id的，需要特殊处理
                log.info("删除整个业务版图映射，refId: {}, territoryId: {}",
                    reqVO.getRefId(), reqVO.getTerritoryId());

                // 1. 先删除指定territory_id的产品映射记录
                int productMappingDeletedCount = diTerritoryProductMappingMapper.delete(
                    Wrappers.<DiTerritoryProductMapping>lambdaQuery()
                        .eq(DiTerritoryProductMapping::getRefId, reqVO.getRefId())
                        .eq(DiTerritoryProductMapping::getTerritoryId, reqVO.getTerritoryId())
                );

                log.info("删除指定territory的产品映射记录完成，删除记录数: {}", productMappingDeletedCount);

                // 2. 检查是否还有其他territory_id的产品映射记录引用同一个ref_id
                Long remainingProductMappingCount = diTerritoryProductMappingMapper.selectCount(
                    Wrappers.<DiTerritoryProductMapping>lambdaQuery()
                        .eq(DiTerritoryProductMapping::getRefId, reqVO.getRefId())
                );

                log.info("检查剩余产品映射记录数: {}", remainingProductMappingCount);

                // 3. 如果还有其他产品映射记录引用同一个ref_id，需要先删除所有相关的产品映射
                if (remainingProductMappingCount > 0) {
                    log.warn("发现其他territory的产品映射记录仍引用ref_id: {}，需要先删除所有相关产品映射以避免外键约束违反",
                        reqVO.getRefId());

                    // 删除所有引用该ref_id的产品映射记录
                    int allProductMappingDeletedCount = diTerritoryProductMappingMapper.delete(
                        Wrappers.<DiTerritoryProductMapping>lambdaQuery()
                            .eq(DiTerritoryProductMapping::getRefId, reqVO.getRefId())
                    );

                    log.info("删除所有相关产品映射记录完成，删除记录数: {}", allProductMappingDeletedCount);
                }

                // 4. 最后删除业务版图映射记录
                int territoryMappingDeletedCount = territoryMappingMapper.delete(
                    Wrappers.<DiTerritoryMapping>lambdaQuery()
                        .eq(DiTerritoryMapping::getRefId, reqVO.getRefId())
                        .eq(DiTerritoryMapping::getTerritoryId, reqVO.getTerritoryId())
                );

                log.info("删除业务版图映射记录完成，删除记录数: {}", territoryMappingDeletedCount);
            }

        } catch (Exception e) {
            log.error("删除营销版图映射事务执行失败，refId: {}, territoryId: {}, 错误详情: {}",
                reqVO.getRefId(), reqVO.getTerritoryId(), e.getMessage(), e);
            throw e; // 重新抛出异常，触发事务回滚
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void clueToNiche(String clueNo, String nicheNo) {
        List<DiTerritoryMapping> territoryMappingList =
                territoryMappingMapper.selectList(Wrappers.<DiTerritoryMapping>lambdaQuery().eq(DiTerritoryMapping::getRefId, clueNo))
                .stream().map(mapping -> new DiTerritoryMapping(mapping, nicheNo)).toList();
        List<DiTerritoryProductMapping> territoryProductMappingList =
                diTerritoryProductMappingMapper.selectList(Wrappers.<DiTerritoryProductMapping>lambdaQuery().eq(DiTerritoryProductMapping::getRefId, clueNo))
                        .stream().map(mapping -> new DiTerritoryProductMapping(mapping, nicheNo)).toList();
        territoryMappingMapper.insert(territoryMappingList);
        diTerritoryProductMappingMapper.insert(territoryProductMappingList);
    }

}