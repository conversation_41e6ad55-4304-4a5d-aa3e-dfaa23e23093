<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dyd.di.marketing.mapper.DiTerritoryProductMappingMapper">

    <!-- 批量插入产品映射 -->
    <insert id="insertBatch" parameterType="java.util.List">
        INSERT INTO di_territory_product_mapping
        (ref_id, territory_id, product_id, product_json, product_info, create_time, update_time)
        VALUES
        <foreach collection="list" item="item" separator=",">
            (#{item.refId}, #{item.territoryId}, #{item.productId},
             #{item.productJson}, #{item.productInfo},
             #{item.createTime}, #{item.updateTime})
        </foreach>
    </insert>

</mapper>