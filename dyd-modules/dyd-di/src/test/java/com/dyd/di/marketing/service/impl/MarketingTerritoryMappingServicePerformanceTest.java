package com.dyd.di.marketing.service.impl;

import com.alibaba.fastjson.JSON;
import com.dyd.common.core.domain.R;
import com.dyd.di.marketing.domain.vo.MarketingTerritoryMappingVO;
import com.dyd.di.marketing.mapper.DiTerritoryMappingMapper;
import com.dyd.di.marketing.mapper.DiTerritoryProductMappingMapper;
import com.dyd.di.matrix.dto.MatrixProductDetailDTO;
import com.dyd.di.matrix.pojo.response.QueryProductByTerritoryResponse;
import com.dyd.di.matrix.service.DiMatrixProductService;
import com.dyd.di.matrix.service.DiMatrixTerritoryService;
import com.dyd.di.matrix.vo.OperateMatrixProductVO;
import com.dyd.di.storage.redisson.AcquiredLockWorker;
import com.dyd.di.storage.redisson.RedissonLock;
import com.google.common.collect.Lists;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.util.StopWatch;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.*;

/**
 * 营销版图映射服务性能测试
 */
@ExtendWith(MockitoExtension.class)
public class MarketingTerritoryMappingServicePerformanceTest {

    @Mock
    private DiTerritoryMappingMapper territoryMappingMapper;

    @Mock
    private DiTerritoryProductMappingMapper diTerritoryProductMappingMapper;

    @Mock
    private DiMatrixProductService diMatrixProductService;

    @Mock
    private DiMatrixTerritoryService diMatrixTerritoryService;

    @Mock
    private RedissonLock redissonLock;

    @InjectMocks
    private MarketingTerritoryMappingServiceImpl marketingTerritoryMappingService;

    private MarketingTerritoryMappingVO testVO;

    @BeforeEach
    void setUp() {
        // 创建测试数据 - 模拟100个产品的批量添加
        testVO = new MarketingTerritoryMappingVO();
        testVO.setRefId("test-ref-001");
        testVO.setTerritoryId(1001L);
        testVO.setTerritoryJson("{\"territoryName\":\"测试版图\"}");

        // 创建包含100个产品的响应数据
        QueryProductByTerritoryResponse response = new QueryProductByTerritoryResponse();
        response.setTitleMap(Map.of("id", "产品ID", "name", "产品名称"));
        
        List<Map<String, String>> productDataList = Lists.newArrayList();
        for (int i = 1; i <= 100; i++) {
            Map<String, String> productData = new HashMap<>();
            productData.put("id", String.valueOf(i));
            productData.put("name", "测试产品" + i);
            productDataList.add(productData);
        }
        response.setData(productDataList);
        response.setTotal(100L);

        testVO.setProductJson(JSON.toJSONString(response));
    }

    /**
     * 测试批量添加产品映射的性能
     */
    @Test
    void testBatchAddPerformance() throws Exception {
        // Mock 分布式锁
        doAnswer(invocation -> {
            AcquiredLockWorker worker = invocation.getArgument(1);
            return worker.invokeAfterLockAcquire();
        }).when(redissonLock).lock(anyString(), any(AcquiredLockWorker.class), eq(10));

        // Mock 数据库查询
        when(territoryMappingMapper.selectCount(any())).thenReturn(0L);
        when(diTerritoryProductMappingMapper.selectCount(any())).thenReturn(0L);

        // Mock 产品详情服务调用
        MatrixProductDetailDTO mockProductDetail = new MatrixProductDetailDTO();
        mockProductDetail.setId(1L);
        mockProductDetail.setProductName("测试产品");
        when(diMatrixProductService.getMatrixProductDetail(any(OperateMatrixProductVO.class)))
                .thenReturn(R.ok(mockProductDetail));

        // Mock 业务版图信息服务调用
        when(diMatrixTerritoryService.getMatrixTerritoryInfo(any())).thenReturn(null);

        // Mock 批量插入成功
        when(diTerritoryProductMappingMapper.insertBatch(any())).thenReturn(100);

        // 执行性能测试
        StopWatch stopWatch = new StopWatch("批量添加性能测试");
        stopWatch.start("批量添加100个产品映射");

        marketingTerritoryMappingService.add(testVO);

        stopWatch.stop();

        // 输出性能结果
        System.out.println("=== 性能测试结果 ===");
        System.out.println("总耗时: " + stopWatch.getTotalTimeMillis() + "ms");
        System.out.println("处理产品数量: 100");
        System.out.println("平均每个产品耗时: " + (stopWatch.getTotalTimeMillis() / 100.0) + "ms");

        // 验证批量插入被调用
        verify(diTerritoryProductMappingMapper, times(1)).insertBatch(any());
        
        // 验证产品详情服务被调用100次（批量获取）
        verify(diMatrixProductService, times(100)).getMatrixProductDetail(any());
        
        // 验证删除操作被调用
        verify(diTerritoryProductMappingMapper, times(1)).delete(any());
    }

    /**
     * 测试单个产品添加的性能
     */
    @Test
    void testSingleProductAddPerformance() throws Exception {
        // 设置单个产品测试数据
        testVO.setProductId(1L);
        testVO.setProductJson(null); // 单个产品不需要productJson

        // Mock 分布式锁
        doAnswer(invocation -> {
            AcquiredLockWorker worker = invocation.getArgument(1);
            return worker.invokeAfterLockAcquire();
        }).when(redissonLock).lock(anyString(), any(AcquiredLockWorker.class), eq(10));

        // Mock 数据库查询
        when(territoryMappingMapper.selectCount(any())).thenReturn(0L);

        // Mock 产品详情服务调用
        MatrixProductDetailDTO mockProductDetail = new MatrixProductDetailDTO();
        mockProductDetail.setId(1L);
        mockProductDetail.setProductName("测试产品");
        when(diMatrixProductService.getMatrixProductDetail(any(OperateMatrixProductVO.class)))
                .thenReturn(R.ok(mockProductDetail));

        // Mock 业务版图信息服务调用
        when(diMatrixTerritoryService.getMatrixTerritoryInfo(any())).thenReturn(null);

        // 执行性能测试
        StopWatch stopWatch = new StopWatch("单个产品添加性能测试");
        stopWatch.start("添加单个产品映射");

        marketingTerritoryMappingService.add(testVO);

        stopWatch.stop();

        // 输出性能结果
        System.out.println("=== 单个产品添加性能测试结果 ===");
        System.out.println("总耗时: " + stopWatch.getTotalTimeMillis() + "ms");

        // 验证单个插入被调用
        verify(diTerritoryProductMappingMapper, times(1)).insert(any());
        
        // 验证产品详情服务被调用1次
        verify(diMatrixProductService, times(1)).getMatrixProductDetail(any());
    }

    /**
     * 测试并发场景下的性能
     */
    @Test
    void testConcurrentAddPerformance() throws Exception {
        // Mock 分布式锁模拟并发等待
        doAnswer(invocation -> {
            // 模拟锁等待时间
            Thread.sleep(10);
            AcquiredLockWorker worker = invocation.getArgument(1);
            return worker.invokeAfterLockAcquire();
        }).when(redissonLock).lock(anyString(), any(AcquiredLockWorker.class), eq(10));

        // Mock 其他依赖
        when(territoryMappingMapper.selectCount(any())).thenReturn(0L);
        when(diTerritoryProductMappingMapper.selectCount(any())).thenReturn(0L);
        when(diMatrixProductService.getMatrixProductDetail(any(OperateMatrixProductVO.class)))
                .thenReturn(R.ok(new MatrixProductDetailDTO()));
        when(diMatrixTerritoryService.getMatrixTerritoryInfo(any())).thenReturn(null);
        when(diTerritoryProductMappingMapper.insertBatch(any())).thenReturn(100);

        // 执行并发测试
        StopWatch stopWatch = new StopWatch("并发性能测试");
        stopWatch.start("并发添加测试");

        marketingTerritoryMappingService.add(testVO);

        stopWatch.stop();

        // 输出性能结果
        System.out.println("=== 并发性能测试结果 ===");
        System.out.println("总耗时（包含锁等待）: " + stopWatch.getTotalTimeMillis() + "ms");

        // 验证分布式锁被正确调用
        verify(redissonLock, times(1)).lock(anyString(), any(AcquiredLockWorker.class), eq(10));
    }
}
